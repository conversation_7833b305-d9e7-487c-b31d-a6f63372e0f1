import { NextIntlClientProvider } from "next-intl";
import { getMessages } from "next-intl/server";
import { ptBR, enUS } from "@clerk/localizations";
import { ClerkProvider } from "@clerk/nextjs";

type Props = {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
};

export default async function LocaleLayout({
  children,
  params,
}: Props) {
  const { locale } = await params;
  const messages = await getMessages();
  const clerkLocale = locale === "br" ? ptBR : enUS;

  return (
    <ClerkProvider localization={clerkLocale}>
      <NextIntlClientProvider messages={messages} locale={locale}>
        {children}
      </NextIntlClientProvider>
    </ClerkProvider>
  );
}