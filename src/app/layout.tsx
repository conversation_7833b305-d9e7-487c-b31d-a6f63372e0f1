import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/nextjs";
import type { Metadata } from "next";
import { Inter as FontSans } from "next/font/google";
import { NextIntlClientProvider } from "next-intl";
import { ptBR, enUS } from "@clerk/localizations";
import { headers } from "next/headers";
import { cn } from "@/lib/utils";
import "@/styles/globals.css";

export const metadata: Metadata = {
  title: "t-chr",
  description: "Publique cursos online sem complicação.",
};

const fontSans = FontSans({
  subsets: ["latin"],
  variable: "--font-sans",
});

// Import all language files
import enMessages from "@/languages/en.json";
import brMessages from "@/languages/br.json";

type Locale = "en" | "br";

// Create a more flexible type for messages
type DeepPartial<T> = T extends object
  ? {
      [P in keyof T]?: DeepPartial<T[P]>;
    }
  : T;

type MessageType = typeof enMessages;
type FlexibleMessageType = DeepPartial<MessageType>;

const messages: Record<Locale, FlexibleMessageType> = {
  en: enMessages,
  br: brMessages,
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // Determine the locale from the headers
  const headersList = await headers();
  const pathname = headersList.get("x-invoke-path") || "";
  const locale = (pathname.split("/")[1] || "en") as Locale;

  // Validate the locale
  if (!["en", "br"].includes(locale)) {
    console.error(`Unsupported locale: ${locale}`);
  }

  const clerkLocale = locale === "br" ? ptBR : enUS;

  return (
    <html lang={locale} className="scroll-smooth">
      <ClerkProvider localization={clerkLocale} afterSignOutUrl="/">
        <NextIntlClientProvider messages={messages[locale]} locale={locale}>
          <body
            className={cn(
              "min-h-screen bg-background font-sans antialiased",
              fontSans.variable
            )}
          >
            {children}
          </body>
        </NextIntlClientProvider>
      </ClerkProvider>
    </html>
  );
}

// Define the locales you want to support
export function generateStaticParams() {
  return [{ locale: "en" }, { locale: "br" }];
}

