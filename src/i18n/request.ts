import { getRequestConfig } from "next-intl/server";

// Can be imported from a shared config
const locales = ["br", "en"];

export default getRequestConfig(async ({requestLocale}) => {
  // Validate that the incoming `locale` parameter is valid
  if (!locales.includes(locale as any)) {
    // In Next.js 15, we can't use notFound() in root layout context
    // Instead, we'll use the default locale
    locale = "en";
  }

  return {
    messages: (await import(`../languages/${locale}.json`)).default,
  };
});
